import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/affiliate_controller.dart';
import '../../models/commission.dart';
import '../../models/affiliate_link.dart';

class AffiliateDashboard extends StatelessWidget {
  const AffiliateDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    final AffiliateController controller = Get.put(AffiliateController());

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('لوحة تحكم المسوق'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadData,
          ),
        ],
      ),
      body: Column(
        children: [
          // إحصائيات سريعة
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Obx(() => _buildStatsCards(controller.stats)),
          ),
          
          // التبويبات
          Container(
            color: Colors.white,
            child: TabBar(
              controller: controller.tabController,
              labelColor: Colors.teal,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.teal,
              tabs: const [
                Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
                Tab(text: 'العمولات', icon: Icon(Icons.monetization_on)),
                Tab(text: 'الروابط', icon: Icon(Icons.link)),
                Tab(text: 'التقارير', icon: Icon(Icons.analytics)),
              ],
            ),
          ),
          
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              children: [
                _buildOverviewTab(controller),
                _buildCommissionsTab(controller),
                _buildLinksTab(controller),
                _buildReportsTab(controller),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showCreateLinkDialog(context, controller),
        backgroundColor: Colors.teal,
        icon: const Icon(Icons.add_link),
        label: const Text('رابط جديد'),
      ),
    );
  }

  Widget _buildStatsCards(Map<String, dynamic> stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الأرباح',
            '\$${stats['totalEarnings']?.toStringAsFixed(2) ?? '0.00'}',
            Icons.account_balance_wallet,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'أرباح معلقة',
            '\$${stats['pendingEarnings']?.toStringAsFixed(2) ?? '0.00'}',
            Icons.pending,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'معدل التحويل',
            '${stats['conversionRate']?.toStringAsFixed(1) ?? '0.0'}%',
            Icons.trending_up,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(AffiliateController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص الأداء
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص الأداء',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Obx(() => Column(
                    children: [
                      _buildPerformanceRow('إجمالي النقرات', '${controller.stats['totalClicks'] ?? 0}'),
                      _buildPerformanceRow('إجمالي التحويلات', '${controller.stats['totalConversions'] ?? 0}'),
                      _buildPerformanceRow('متوسط قيمة الطلب', '\$${controller.stats['averageOrderValue']?.toStringAsFixed(2) ?? '0.00'}'),
                      _buildPerformanceRow('إجمالي العمولات', '${controller.stats['totalCommissions'] ?? 0}'),
                    ],
                  )),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // أحدث العمولات
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'أحدث العمولات',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      TextButton(
                        onPressed: () => controller.tabController.animateTo(1),
                        child: const Text('عرض الكل'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Obx(() => controller.commissions.isEmpty
                      ? const Center(child: Text('لا توجد عمولات'))
                      : Column(
                          children: controller.commissions
                              .take(3)
                              .map((commission) => _buildCommissionTile(commission, controller))
                              .toList(),
                        )),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // زر طلب الصرف
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: controller.requestPayout,
              icon: const Icon(Icons.payment),
              label: const Text('طلب صرف العمولات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildCommissionsTab(AffiliateController controller) {
    return Column(
      children: [
        // فلاتر
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<CommissionStatus?>(
                  value: controller.statusFilter.value,
                  decoration: const InputDecoration(
                    labelText: 'فلتر الحالة',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('جميع الحالات')),
                    ...CommissionStatus.values.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(controller.getCommissionStatusText(status)),
                    )),
                  ],
                  onChanged: controller.applyStatusFilter,
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: controller.clearFilters,
                child: const Text('مسح الفلاتر'),
              ),
            ],
          ),
        ),
        
        // قائمة العمولات
        Expanded(
          child: Obx(() => controller.commissions.isEmpty
              ? const Center(child: Text('لا توجد عمولات'))
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: controller.commissions.length,
                  itemBuilder: (context, index) {
                    final commission = controller.commissions[index];
                    return _buildCommissionCard(commission, controller);
                  },
                )),
        ),
      ],
    );
  }

  Widget _buildCommissionTile(Commission commission, AffiliateController controller) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: controller.getCommissionStatusColor(commission.status),
        child: Text(
          commission.formattedCommissionAmount.substring(1, 3),
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ),
      title: Text('طلب #${commission.orderId}'),
      subtitle: Text(controller.getCommissionStatusText(commission.status)),
      trailing: Text(
        commission.formattedCommissionAmount,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildCommissionCard(Commission commission, AffiliateController controller) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'طلب #${commission.orderId}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: controller.getCommissionStatusColor(commission.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    controller.getCommissionStatusText(commission.status),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('قيمة الطلب: ${commission.formattedOrderAmount}'),
                Text(
                  'العمولة: ${commission.formattedCommissionAmount}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'تاريخ الإنشاء: ${commission.createdAt.day}/${commission.createdAt.month}/${commission.createdAt.year}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinksTab(AffiliateController controller) {
    return Obx(() => controller.affiliateLinks.isEmpty
        ? const Center(child: Text('لا توجد روابط تسويقية'))
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: controller.affiliateLinks.length,
            itemBuilder: (context, index) {
              final link = controller.affiliateLinks[index];
              return _buildLinkCard(link, controller);
            },
          ));
  }

  Widget _buildLinkCard(AffiliateLink link, AffiliateController controller) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  controller.getLinkTypeText(link.type),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () => controller.copyLinkToClipboard(link.shortUrl),
                    ),
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () => controller.shareLink(link.shortUrl, 'رابط تسويقي'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                link.shortUrl,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildLinkStat('النقرات', '${link.clickCount}', Colors.blue),
                _buildLinkStat('التحويلات', '${link.conversionCount}', Colors.green),
                _buildLinkStat('الأرباح', '\$${link.totalEarnings.toStringAsFixed(2)}', Colors.orange),
                _buildLinkStat('معدل التحويل', '${link.conversionRate.toStringAsFixed(1)}%', Colors.purple),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildReportsTab(AffiliateController controller) {
    return const Center(
      child: Text('التقارير - قريباً'),
    );
  }

  void _showCreateLinkDialog(BuildContext context, AffiliateController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء رابط تسويقي جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() => DropdownButtonFormField<LinkType>(
              value: controller.selectedLinkType.value,
              decoration: const InputDecoration(
                labelText: 'نوع الرابط',
                border: OutlineInputBorder(),
              ),
              items: LinkType.values.map((type) => DropdownMenuItem(
                value: type,
                child: Text(controller.getLinkTypeText(type)),
              )).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.selectedLinkType.value = value;
                }
              },
            )),
            const SizedBox(height: 16),
            Obx(() => controller.selectedLinkType.value == LinkType.product
                ? TextField(
                    controller: controller.productUrlController,
                    decoration: const InputDecoration(
                      labelText: 'رابط المنتج',
                      border: OutlineInputBorder(),
                      hintText: 'https://eyewearstore.com/product/1',
                    ),
                  )
                : TextField(
                    controller: controller.customUrlController,
                    decoration: const InputDecoration(
                      labelText: 'رابط مخصص (اختياري)',
                      border: OutlineInputBorder(),
                      hintText: 'https://eyewearstore.com',
                    ),
                  )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          Obx(() => ElevatedButton(
            onPressed: controller.isLoading.value ? null : controller.createAffiliateLink,
            child: controller.isLoading.value
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('إنشاء'),
          )),
        ],
      ),
    );
  }
}
